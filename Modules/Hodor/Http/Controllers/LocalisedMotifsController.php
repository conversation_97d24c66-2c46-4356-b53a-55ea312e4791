<?php

namespace Modules\Hodor\Http\Controllers;

use App\LocalisedMotif;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Hodor\Http\Requests\LocalisedMotifRequest;

class LocalisedMotifsController extends HodorController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        $this->view_data['localised_motifs'] = LocalisedMotif::orderBy('created_at', 'desc')->paginate(20);
        $this->view_data['page_title'] = 'Localised Motifs';

        return view('hodor::localised-motifs.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title'] = 'Create New Localised Motif';

        return view('hodor::localised-motifs.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param LocalisedMotifRequest $request
     * @return Renderable
     */
    public function store(LocalisedMotifRequest $request)
    {
        $localisedMotif = LocalisedMotif::create($request->validated());

        return redirect()->route('hodor.localised-motifs.edit', $localisedMotif->id)
            ->withSuccess('Entity with id: ' . $localisedMotif->id . ' was successfully saved!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        $localisedMotif = LocalisedMotif::findOrFail($id);

        $this->view_data['localised_motif'] = $localisedMotif;
        $this->view_data['page_title'] = 'Localised Motif: ' . $id;

        return view('hodor::localised-motifs.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param LocalisedMotifRequest $request
     * @param int $id
     * @return Renderable
     */
    public function update(LocalisedMotifRequest $request, $id)
    {
        $localisedMotif = LocalisedMotif::findOrFail($id);

        $localisedMotif->update($request->validated());

        return redirect()->route('hodor.localised-motifs.edit', $localisedMotif->id)
            ->withSuccess('Entity with id: ' . $localisedMotif->id . ' was successfully saved!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        $deleteItem = LocalisedMotif::find($id);

        if ($deleteItem->delete()) {
            return redirect()->route('hodor.localised-motifs.index')
                ->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
        } else {
            return redirect()->route('hodor.localised-motifs.index')
                ->with('error', 'An error occured!');
        }
    }
}
