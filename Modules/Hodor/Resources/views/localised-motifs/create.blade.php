@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Localised Motif Details</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::open(array('method' => 'POST', 'class' => 'main', 'route' => array('hodor.localised-motifs.store'))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('title', 'Title*') !!}
                                {!! Form::text('title', null, ['class' => 'form-control' . ($errors->has('title') ? ' is-invalid' : null)]) !!}
                                @if ($errors->has('title'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('title') }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                {!! Form::label('description', 'Description*') !!}
                                {!! Form::textarea('description', null, ['class' => 'form-control' . ($errors->has('description') ? ' is-invalid' : null), 'rows' => 4]) !!}
                                @if ($errors->has('description'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('description') }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
                    <a href="{{ route('hodor.localised-motifs.index') }}" class="btn btn-secondary ml-2">Cancel</a>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection
