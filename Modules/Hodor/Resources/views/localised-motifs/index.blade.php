@extends('hodor::layouts.master')

@section('content')
<section class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"></div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="{{ route('hodor.localised-motifs.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Localised Motif</a>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div>{{ $localised_motifs->count() }} of {{ $localised_motifs->total() }} items</div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Description</th>
                                    <th>Posts (pub.)</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($localised_motifs as $localisedMotif)
                                <tr>
                                    <td><strong><a href="{{ route('hodor.localised-motifs.edit', $localisedMotif->id) }}">{{ $localisedMotif->id }}</a></strong></td>
                                    <td>{{ $localisedMotif->title }}</td>
                                    <td>{{ Str::limit($localisedMotif->description, 50) }}</td>
                                    <td>@if($localisedMotif->posts()->exists()) {{ $localisedMotif->posts()->count() }} ({{ $localisedMotif->publishedPosts()->count() }}) @else - @endif</td>
                                    <td class="text-right">
                                        <a class="btn btn-outline-info" title="Edit" href="{{ route('hodor.localised-motifs.edit', $localisedMotif->id) }}">
                                            <i class="fas fa-pen"></i>
                                        </a>
                                        {!! Form::open(array('method' => 'DELETE', 'class' => 'btn', 'route' => array('hodor.localised-motifs.destroy', $localisedMotif->id))) !!}
                                            {!! Form::token() !!}
                                            <button class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this item?');"><i class="fas fa-trash"></i></button>
                                        {!! Form::close() !!}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>{{ $localised_motifs->links() }}</div>
</section>
@endsection
