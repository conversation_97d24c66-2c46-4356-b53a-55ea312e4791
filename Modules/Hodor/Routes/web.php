<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Modules\Hodor\Http\Controllers\AnalyticsController;
use Modules\Hodor\Http\Controllers\ContactLeadsController;
use Modules\Hodor\Http\Controllers\CustomersController;
use Modules\Hodor\Http\Controllers\GoogleReviewsController;
use Modules\Hodor\Http\Controllers\ListingsController;
use Modules\Hodor\Http\Controllers\LocalisedMotifsController;
use Modules\Hodor\Http\Controllers\MotifsController;
use Modules\Hodor\Http\Controllers\OffersController;
use Modules\Hodor\Http\Controllers\PhotosController;
use Modules\Hodor\Http\Controllers\PostsController;
use Modules\Hodor\Http\Controllers\ReservationsController;
use Modules\Hodor\Http\Controllers\SearchQueriesController;
use Modules\Hodor\Http\Controllers\SupergroupsController;
use Modules\Hodor\Http\Controllers\TagsController;
// dashboard
Route::get('/', 'DashboardController@index')->name('dashboard');

// google reviews
Route::get('google-reviews/{review}/texts/', 'GoogleReviewsController@textEdit')->name('google-reviews.texts.edit');
Route::put('google-reviews/{review}/texts/', 'GoogleReviewsController@textUpdate')->name('google-reviews.texts.update');
Route::resource('google-reviews', GoogleReviewsController::class)
    ->except('show');

// analytics
Route::resource('analytics', AnalyticsController::class);
Route::get('budget_analytics', 'AnalyticsController@budget')->name('analytics.budget');
Route::get('budget-comparisons', 'AnalyticsController@budgetComparisons')->name('analytics.budget.comparisons');
Route::post('budget-comparisons-submit', 'AnalyticsController@submitBudgetComparisons')->name('analytics.budget.comparisons.submit');
Route::get('budget-per-day', 'AnalyticsController@budgetPerDay')->name('analytics.budget.perDay');
Route::get('customer-retention', 'AnalyticsController@customerRetention')->name('analytics.customer.retention');
Route::get('fleet-utilization', 'AnalyticsController@fleetUtilization')->name('analytics.fleet.utilization');
Route::get('seasonal-performance', 'AnalyticsController@seasonalPerformance')->name('analytics.seasonal');

// customers
Route::resource('customers', CustomersController::class)
    ->except('show');

// supergroups
Route::resource('supergroups', SupergroupsController::class)
    ->except('show');

// blog / posts
Route::get('posts/{post}/photos/', 'PostsController@photosIndex')->name('posts.photos.index');
Route::post('posts/{post}/photos/', 'PostsController@photoStore')->name('posts.photos.store');
Route::resource('posts', PostsController::class)
    ->except('show');

// blog / auto translations
Route::post('translations/suggest', 'TranslationsController@suggest')->name('translations.suggest');

// blog / tags
Route::resource('tags', TagsController::class)
    ->except('show');

// blog / searches
Route::resource('search_queries', SearchQueriesController::class)
    ->only('index');

// listings
Route::get('listings/make-media', 'ListingsController@makeMedia');
Route::get('listings/{listing}/photos/', 'ListingsController@photosIndex')->name('listings.photos.index');
Route::post('listings/{listing}/photos/', 'ListingsController@photoStore')->name('listings.photos.store');
Route::get('listings/{listing}/texts/', 'ListingsController@textEdit')->name('listings.texts.edit');
Route::put('listings/{listing}/texts/', 'ListingsController@textUpdate')->name('listings.texts.update');
Route::resource('listings', ListingsController::class)
    ->except('show');

// photos
Route::put('photos/{photo}/make-main', 'PhotosController@makeMain')->name('photos.makeMain');
Route::resource('photos', PhotosController::class)
    ->except(['show', 'create', 'store']);

// motifs
Route::resource('motifs', MotifsController::class)
    ->except('show');

// localised motifs
Route::resource('localised-motifs', LocalisedMotifsController::class)
    ->except('show');

// price history
Route::get('group/select', 'PriceHistoryController@index')->name('price-history.index');
Route::get('price/history/{group?}', 'PriceHistoryController@group')->name('price-history.group');

// reservations
Route::get('reservations/deleted', 'ReservationsController@indexDeleted')->name('reservations.indexDeleted');
Route::get('reservations/duplicates', 'ReservationsController@indexDuplicates')->name('reservations.indexDuplicates');
Route::put('reservations/unduplicate/{reservation}', 'ReservationsController@updateUnduplicate')->name('reservations.unduplicate');
Route::put('reservations/verifyShow/{reservation}', 'ReservationsController@updateVerifyShow')->name('reservations.verifyShow');
Route::put('reservations/toggleShow/{reservation}', 'ReservationsController@toggleShow')->name('reservations.toggleShow');
Route::resource('reservations', ReservationsController::class)
    ->except(['show', 'create', 'store']);

// offers
Route::put('offers/{offer}/notify', 'OffersController@notify')->name('offers.notify');
Route::put('offers/{offer}/slugify', 'OffersController@slugify')->name('offers.slugify');
Route::resource('offers', OffersController::class)
    ->except(['show', 'delete']);

// contact leads
Route::resource('contact-leads', ContactLeadsController::class)
    ->except(['show', 'create', 'store', 'update']);

// groups
Route::get('groups/{group}/texts/', 'GroupsController@textEdit')->name('groups.texts.edit');
Route::put('groups/{group}/texts/', 'GroupsController@textUpdate')->name('groups.texts.update');
Route::put('groups/{group}/base-price', 'GroupsController@updateBasePrice')->name('groups.updateBasePrice');
Route::post('groups/sort-order', 'GroupsController@sortOrder')->name('groups.sortOrder');
Route::resource('groups', 'GroupsController')
    ->except(['show', 'destroy']);
